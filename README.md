# VCF Exam Practice Applications

This repository contains two VMware Cloud Foundation (VCF) exam practice applications:

## 📚 Available Exams

### 1. VCF Administrator Exam (2V0-11.25)
- **Location**: `administrator exam/` folder
- **Questions**: 60 practice questions
- **Features**: Complete exam simulator with practice and real exam modes

### 2. VCF Architect Exam (2V0-13.24)
- **Location**: `architect exam/` folder
- **Questions**: 20 practice questions (extracted from ExamTopics)
- **Features**: Community voting data integration, practice and real exam modes

## 🌟 Key Features

### VCF Architect Exam (NEW!)
- **Community Voting Integration**: Questions include community voting data from ExamTopics
- **Prioritized Answers**: Correct answers determined by community vote distribution
- **Visual Voting Display**: See how the community voted on each question
- **ExamTopics Source**: Questions extracted from actual ExamTopics 2V0-13.24 exam data

### Both Applications Include:
- **Two Exam Modes**:
  - Practice Mode: Immediate feedback and explanations
  - Real Exam Mode: Results shown at completion
- **Randomization Options**:
  - Randomize question order
  - Randomize answer choices
- **Progress Tracking**: Visual progress indicators and scoring
- **Theme Support**: Multiple visual themes (gradient, dark, light)
- **Answer Review**: Complete reference guide with search and filtering
- **Responsive Design**: Works on desktop and mobile devices

## 🚀 Getting Started

1. **For VCF Administrator Exam**:
   ```
   Open: administrator exam/index.html
   ```

2. **For VCF Architect Exam**:
   ```
   Open: architect exam/index.html
   ```

## 📊 Community Voting Feature (Architect Exam)

The VCF Architect exam includes unique community voting data that shows:
- How many users voted for each answer option
- Vote percentages and distribution
- Most voted answers highlighted
- Comparison between official answers and community consensus

This helps you understand not just the correct answers, but also how the community interprets each question.
