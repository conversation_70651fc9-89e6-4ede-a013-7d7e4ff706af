/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.header {
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header h1 {
    color: #2c3e50;
    margin: 0;
    font-size: 2rem;
}

.theme-selector {
    display: flex;
    align-items: center;
    gap: 8px;
}

.theme-selector label {
    font-size: 1.2rem;
    cursor: pointer;
}

#theme-select {
    padding: 4px 8px;
    border: 2px solid #3498db;
    border-radius: 6px;
    background: white;
    color: #2c3e50;
    font-size: 0.85rem;
    cursor: pointer;
    min-width: 80px;
}

#theme-select:focus {
    outline: none;
    border-color: #2980b9;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.exam-info {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.exam-info span {
    background: #3498db;
    color: white;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.screen {
    display: none;
    flex: 1;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.screen.active {
    display: flex;
    flex-direction: column;
}

/* Start Screen */
.start-container {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.start-container h2 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 2.5rem;
}

.start-container p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    color: #555;
}

.mode-selection {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.mode-card {
    background: #f8f9fa;
    border: 3px solid #e9ecef;
    border-radius: 10px;
    padding: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.mode-card:hover {
    border-color: #3498db;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.mode-card.selected {
    border-color: #27ae60;
    background: #e8f5e8;
}

.mode-card h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.5rem;
}

.mode-card p {
    color: #666;
    margin-bottom: 8px;
    text-align: left;
    font-size: 1rem;
}



/* Buttons */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #2980b9;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #7f8c8d;
}

.btn-success {
    background: #27ae60;
    color: white;
}

.btn-success:hover:not(:disabled) {
    background: #229954;
}

.btn-warning {
    background: #f39c12;
    color: white;
}

.btn-warning:hover:not(:disabled) {
    background: #e67e22;
}

.btn-home {
    background: #6c757d;
    color: white;
    padding: 8px 16px;
    font-size: 0.9rem;
}

.btn-home:hover:not(:disabled) {
    background: #5a6268;
}

/* Question Screen */
.question-container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.question-header {
    margin-bottom: 30px;
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.question-title-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 15px;
}

.question-number {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
}

.question-controls {
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3498db, #27ae60);
    transition: width 0.3s ease;
    width: 0%;
}

.question-content {
    flex: 1;
    margin-bottom: 20px;
}

#question-text {
    font-size: 0.95rem;
    color: #2c3e50;
    margin-bottom: 18px;
    line-height: 1.4;
}

.options-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.option {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    padding: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: flex-start;
    gap: 12px;
    font-size: 0.95rem;
}

.option:hover {
    border-color: #3498db;
    background: #e3f2fd;
}

.option.selected {
    border-color: #27ae60;
    background: #e8f5e8;
}

.option.correct {
    border-color: #27ae60;
    background: #d4edda;
}

.option.incorrect {
    border-color: #e74c3c;
    background: #f8d7da;
}

.option.correct-highlight {
    background: #e8f5e8;
    border-color: #27ae60;
    color: #1e7e34;
    animation: highlight-pulse 1s ease-in-out;
}

@keyframes highlight-pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

.option-letter {
    font-weight: bold;
    color: #2c3e50;
    min-width: 18px;
    font-size: 0.9rem;
}

.option-text {
    flex: 1;
    color: #333;
    font-size: 0.9rem;
    line-height: 1.3;
}

/* Feedback Section */
.feedback-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.feedback-result {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.feedback-result.correct {
    color: #27ae60;
}

.feedback-result.incorrect {
    color: #e74c3c;
}

.feedback-explanation {
    color: #555;
    line-height: 1.6;
}

/* Community Voting Section */
.community-voting-section {
    background: rgba(240, 248, 255, 0.95);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    border-left: 4px solid #007bff;
}

.community-voting-content h4 {
    color: #007bff;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.community-voting-data {
    margin-bottom: 15px;
}

.voting-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    margin: 5px 0;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 5px;
    border: 1px solid #dee2e6;
}

.voting-option.most-voted {
    background: rgba(40, 167, 69, 0.1);
    border-color: #28a745;
    font-weight: bold;
}

.voting-answer {
    font-weight: 500;
}

.voting-count {
    background: #007bff;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.85rem;
}

.voting-count.most-voted {
    background: #28a745;
}

.voting-note {
    color: #6c757d;
    font-style: italic;
    margin: 0;
    text-align: center;
}

/* Practice Mode Controls */
.practice-controls {
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;
}

.practice-controls .btn {
    padding: 6px 12px;
    font-size: 0.8rem;
    min-width: auto;
    border-radius: 4px;
}

/* Exam Controls */
.exam-controls {
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;
}

.exam-controls .btn {
    padding: 6px 12px;
    font-size: 0.8rem;
    min-width: auto;
    border-radius: 4px;
}

/* Small button variant */
.btn-sm {
    padding: 6px 12px;
    font-size: 0.8rem;
    min-width: auto;
}

.question-navigation {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.review-navigation {
    display: flex;
    justify-content: center;
    margin-top: 15px;
}

.btn-outline {
    background: transparent;
    border: 2px solid #3498db;
    color: #3498db;
}

.btn-outline:hover {
    background: #3498db;
    color: white;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

/* Results Screen */
.results-container {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.results-container h2 {
    color: #2c3e50;
    margin-bottom: 30px;
    font-size: 2.5rem;
}

.score-summary {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 40px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.score-circle {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: conic-gradient(#27ae60 0deg, #27ae60 0deg, #e9ecef 0deg);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.score-circle::before {
    content: '';
    position: absolute;
    width: 160px;
    height: 160px;
    background: white;
    border-radius: 50%;
    z-index: 1;
}

.score-percentage {
    font-size: 2.5rem;
    font-weight: bold;
    color: #2c3e50;
    z-index: 2;
}

.score-fraction {
    font-size: 1.2rem;
    color: #666;
    z-index: 2;
}

.score-details {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.score-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
    padding: 15px 20px;
    border-radius: 8px;
    min-width: 200px;
}

.score-item .label {
    font-weight: 600;
    color: #2c3e50;
}

.score-item .value {
    font-size: 1.2rem;
    font-weight: bold;
}

/* Exam Summary Section */
.exam-summary {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 30px;
    text-align: left;
}

.exam-summary h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    text-align: center;
    font-size: 1.5rem;
}

.summary-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background: white;
    border-radius: 6px;
    border-left: 4px solid #3498db;
}

.stat-label {
    font-weight: 600;
    color: #2c3e50;
}

.stat-value {
    font-weight: bold;
    color: #3498db;
    font-size: 1.1rem;
}

.unanswered-warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 15px;
}

.warning-content {
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.warning-icon {
    font-size: 1.2rem;
    flex-shrink: 0;
}

.warning-text {
    color: #856404;
    line-height: 1.5;
}

.warning-text strong {
    color: #533f03;
}

.results-actions {
    display: flex;
    gap: 20px;
    justify-content: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.review-section {
    text-align: left;
    background: #f8f9fa;
    border-radius: 10px;
    padding: 25px;
}

.review-section h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    text-align: center;
}

.review-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
    max-height: 400px;
    overflow-y: auto;
}

.review-item {
    background: white;
    border-radius: 8px;
    padding: 15px;
    border-left: 4px solid #e9ecef;
}

.review-item.correct {
    border-left-color: #27ae60;
}

.review-item.incorrect {
    border-left-color: #e74c3c;
}

.review-item.unanswered {
    border-left-color: #f39c12;
}

.review-question {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
}

.review-answer {
    font-size: 0.9rem;
    color: #666;
}

.review-summary {
    background: #e3f2fd;
    border: 1px solid #2196f3;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    text-align: center;
}

.review-summary h4 {
    color: #1976d2;
    margin-bottom: 10px;
}

.review-summary p {
    color: #555;
    margin: 0;
}

.review-question-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    font-weight: 600;
}

.review-status-icon {
    font-size: 1.2rem;
}

.review-question-number {
    color: #2c3e50;
    font-weight: bold;
}

.review-status-text {
    font-size: 0.9rem;
    padding: 2px 8px;
    border-radius: 12px;
    background: #f8f9fa;
}

.review-item.correct .review-status-text {
    background: #d4edda;
    color: #155724;
}

.review-item.incorrect .review-status-text {
    background: #f8d7da;
    color: #721c24;
}

.review-item.unanswered .review-status-text {
    background: #fff3cd;
    color: #856404;
}

.review-question-text {
    color: #2c3e50;
    margin-bottom: 8px;
    line-height: 1.5;
}

.review-answer-details {
    font-size: 0.9rem;
    color: #666;
    font-style: italic;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    color: white;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1001;
}

.modal-content {
    background: white;
    padding: 30px;
    border-radius: 10px;
    max-width: 500px;
    width: 90%;
    text-align: center;
}

.modal-content h3 {
    color: #e74c3c;
    margin-bottom: 15px;
}

.modal-content p {
    margin-bottom: 20px;
    color: #666;
}

/* Score Popup Styles */
.score-popup-content {
    max-width: 600px;
    padding: 40px;
}

.score-popup-content h3 {
    color: #27ae60;
    font-size: 2rem;
    margin-bottom: 30px;
    text-align: center;
}

.popup-score-summary {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 40px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.popup-score-circle {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    background: conic-gradient(#27ae60 0deg, #27ae60 0deg, #e9ecef 0deg);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.popup-score-circle::before {
    content: '';
    position: absolute;
    width: 120px;
    height: 120px;
    background: white;
    border-radius: 50%;
    z-index: 1;
}

.popup-score-percentage {
    font-size: 2rem;
    font-weight: bold;
    color: #2c3e50;
    z-index: 2;
}

.popup-score-fraction {
    font-size: 1rem;
    color: #666;
    z-index: 2;
}

.popup-score-details {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.popup-score-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
    padding: 12px 20px;
    border-radius: 8px;
    min-width: 200px;
    border-left: 4px solid #e9ecef;
}

.popup-score-item.correct {
    border-left-color: #27ae60;
    background: #d4edda;
}

.popup-score-item.incorrect {
    border-left-color: #e74c3c;
    background: #f8d7da;
}

.popup-score-item.unanswered {
    border-left-color: #f39c12;
    background: #fff3cd;
}

.popup-label {
    font-weight: 600;
    color: #2c3e50;
}

.popup-value {
    font-size: 1.2rem;
    font-weight: bold;
}

.popup-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.start-actions {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Answers Screen Styles */
.answers-container {
    max-width: 1000px;
    margin: 0 auto;
}

.answers-container h2 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 2.5rem;
    text-align: center;
}

.answers-description {
    text-align: center;
    color: #666;
    margin-bottom: 30px;
    font-size: 1.1rem;
}

.answers-controls {
    display: flex;
    gap: 15px;
    margin-bottom: 30px;
    flex-wrap: wrap;
    justify-content: center;
}

.search-input {
    flex: 1;
    max-width: 400px;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    background: white;
}

.search-input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.filter-select {
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    background: white;
    cursor: pointer;
}

.answers-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 30px;
    max-height: 600px;
    overflow-y: auto;
    padding-right: 10px;
}

.answer-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.answer-item:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
}

.answer-item.hidden {
    display: none;
}

.answer-question {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.1rem;
    line-height: 1.5;
}

.answer-options {
    margin-bottom: 15px;
}

.answer-option {
    padding: 8px 0;
    color: #555;
    display: flex;
    align-items: flex-start;
    gap: 8px;
}

.answer-option.correct {
    color: #27ae60;
    font-weight: 600;
    background: #d4edda;
    padding: 8px 12px;
    border-radius: 6px;
    margin: 2px 0;
}

.answer-correct {
    background: #e8f5e8;
    border: 1px solid #27ae60;
    border-radius: 6px;
    padding: 10px 15px;
    color: #155724;
    font-weight: 600;
}

.answer-type-badge {
    display: inline-block;
    background: #3498db;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    margin-bottom: 10px;
}

.answer-type-badge.multiple {
    background: #e67e22;
}

.answers-actions {
    text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .header h1 {
        font-size: 1.5rem;
    }

    .exam-info {
        justify-content: center;
    }

    .start-container h2 {
        font-size: 2rem;
    }

    .mode-selection {
        grid-template-columns: 1fr;
    }

    .question-navigation {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 200px;
    }

    .score-summary {
        flex-direction: column;
        gap: 20px;
    }

    .score-circle {
        width: 150px;
        height: 150px;
    }

    .score-circle::before {
        width: 120px;
        height: 120px;
    }

    .score-percentage {
        font-size: 2rem;
    }

    .results-actions {
        flex-direction: column;
        align-items: center;
    }

    .practice-controls {
        flex-direction: column;
        align-items: center;
    }

    .practice-controls .btn {
        width: 100%;
        max-width: 180px;
    }

    .question-title-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .question-controls {
        width: 100%;
        justify-content: center;
    }

    .practice-controls,
    .exam-controls {
        justify-content: center;
        width: 100%;
    }

    .practice-controls .btn,
    .exam-controls .btn {
        flex: 1;
        min-width: 80px;
        max-width: 120px;
    }

    .summary-stats {
        grid-template-columns: 1fr;
    }

    .exam-summary {
        padding: 20px 15px;
    }
}

/* Selection Counter Styles */
.selection-counter {
    margin: 10px 0;
    padding: 8px 12px;
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border: 2px solid #2196f3;
    border-radius: 6px;
    display: none;
}

.selection-info {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 0.85rem;
}

.selection-text {
    color: #1976d2;
    font-weight: 600;
}

.selection-complete {
    color: #4caf50;
    font-weight: bold;
    background: rgba(76, 175, 80, 0.1);
    padding: 3px 6px;
    border-radius: 4px;
    font-size: 0.8rem;
}



/* Randomization Options Styles */
.randomization-options {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.randomization-options h4 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.randomization-controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.randomization-option {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: 500;
    color: #2c3e50;
    gap: 10px;
}

.checkbox-label input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: #3498db;
    cursor: pointer;
}

.option-description {
    font-size: 0.9rem;
    color: #666;
    margin-left: 28px;
    margin-top: 0;
}

@media (min-width: 768px) {
    .randomization-controls {
        flex-direction: row;
        gap: 30px;
    }

    .randomization-option {
        flex: 1;
    }
}

/* Theme Styles */
body.theme-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

body.theme-dark {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

body.theme-light {
    background: linear-gradient(135deg, #ecf0f1 0%, #bdc3c7 100%);
}

/* Dark theme adjustments */
body.theme-dark .header {
    background: rgba(52, 73, 94, 0.95);
    color: #ecf0f1;
}

body.theme-dark .header h1 {
    color: #ecf0f1;
}

/* Keep randomization options white in dark theme for better readability */
body.theme-dark .randomization-options {
    background: rgba(255, 255, 255, 0.95);
    color: #2c3e50;
}

body.theme-dark .randomization-options h4,
body.theme-dark .checkbox-label {
    color: #2c3e50;
}

/* Keep home center box white in dark theme for better readability */
body.theme-dark .start-container {
    background: rgba(255, 255, 255, 0.95);
    color: #2c3e50;
}

body.theme-dark .start-container h2,
body.theme-dark .start-container p {
    color: #2c3e50;
}

/* Keep question content and options white in dark theme for better readability */
body.theme-dark .question-content,
body.theme-dark .feedback-section,
body.theme-dark .selection-counter {
    background: rgba(255, 255, 255, 0.95);
    color: #2c3e50;
}

body.theme-dark #question-text,
body.theme-dark .selection-text {
    color: #2c3e50;
}

/* Keep options white with normal colors for better readability */
body.theme-dark .option {
    background: #f8f9fa;
    border-color: #e9ecef;
    color: #333;
}

body.theme-dark .option:hover {
    border-color: #3498db;
    background: #e3f2fd;
}

body.theme-dark .option.selected {
    border-color: #27ae60;
    background: #e8f5e8;
}

body.theme-dark .option.correct {
    border-color: #27ae60 !important;
    background: #d4edda !important;
}

body.theme-dark .option.incorrect {
    border-color: #e74c3c !important;
    background: #f8d7da !important;
}

body.theme-dark .option.correct-highlight {
    background: #e8f5e8 !important;
    border-color: #27ae60 !important;
    color: #1e7e34 !important;
}

body.theme-dark .option-letter,
body.theme-dark .option-text {
    color: #333;
}

body.theme-dark #theme-select {
    background: #34495e;
    color: #ecf0f1;
    border-color: #5d6d7e;
}

body.theme-dark #theme-select:focus {
    border-color: #3498db;
}

/* Dark theme button styles */
body.theme-dark .btn-success {
    background: #27ae60;
    color: white;
}

body.theme-dark .btn-success:hover:not(:disabled) {
    background: #229954;
}

body.theme-dark .btn-secondary {
    background: #95a5a6;
    color: white;
}

body.theme-dark .btn-secondary:hover:not(:disabled) {
    background: #7f8c8d;
}

body.theme-dark .btn-primary {
    background: #3498db;
    color: white;
}

body.theme-dark .btn-primary:hover:not(:disabled) {
    background: #2980b9;
}

body.theme-dark .btn-warning {
    background: #f39c12;
    color: white;
}

body.theme-dark .btn-warning:hover:not(:disabled) {
    background: #e67e22;
}

/* Light theme adjustments */
body.theme-light .header,
body.theme-light .question-content,
body.theme-light .start-container,
body.theme-light .randomization-options {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}
