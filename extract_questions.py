#!/usr/bin/env python3
"""
Extract all 60 VCF Architect exam questions from ExamTopics HTML files
Ensures exact content matching with proper formatting
"""

import re
import json
import html
from pathlib import Path

def clean_html_text(text):
    """Clean HTML text while preserving <br> tags"""
    # Replace <br> and <br/> with actual line breaks for better readability
    text = re.sub(r'<br\s*/?>', '<br>', text)
    # Remove other HTML tags except <br> and <img>
    text = re.sub(r'<(?!br|img)[^>]+>', '', text)
    # Decode HTML entities
    text = html.unescape(text)
    # Clean up extra whitespace but preserve line structure
    text = ' '.join(text.split())
    return text.strip()

def extract_voting_data(html_content, question_id):
    """Extract community voting data from JSON script tags"""
    pattern = rf'<script id="{question_id}" type="application/json">\[(.*?)\]</script>'
    match = re.search(pattern, html_content, re.DOTALL)
    
    if match:
        try:
            voting_json = '[' + match.group(1) + ']'
            voting_data = json.loads(voting_json)
            return voting_data
        except json.JSONDecodeError:
            return []
    return []

def extract_questions_from_html(html_file_path):
    """Extract all questions from a single HTML file"""
    questions = []
    
    with open(html_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Pattern to match question blocks
    question_pattern = r'<div class="card-header text-white bg-primary">\s*Question #(\d+).*?</div>\s*<div class="card-body question-body" data-id="(\d+)">(.*?)<!-- exam-view410'
    
    matches = re.finditer(question_pattern, content, re.DOTALL)
    
    for match in matches:
        question_num = int(match.group(1))
        question_id = match.group(2)
        question_content = match.group(3)
        
        # Extract question text
        text_pattern = r'<p class="card-text">\s*(.*?)\s*</p>'
        text_match = re.search(text_pattern, question_content, re.DOTALL)
        if not text_match:
            continue
            
        question_text = clean_html_text(text_match.group(1))
        
        # Extract options
        options = []
        option_pattern = r'<span class="multi-choice-letter" data-choice-letter="([A-E])">\s*[A-E]\.\s*</span>\s*(.*?)\s*(?:</li>|<span class="badge)'
        option_matches = re.finditer(option_pattern, question_content, re.DOTALL)
        
        for opt_match in option_matches:
            letter = opt_match.group(1)
            option_text = clean_html_text(opt_match.group(2))
            options.append(f"{letter}. {option_text}")
        
        # Extract correct answer
        correct_answer = ""
        correct_pattern = r'<span class="correct-answer">([A-E]+)</span>'
        correct_match = re.search(correct_pattern, content[match.end():match.end()+2000])
        if correct_match:
            correct_answer = correct_match.group(1)
        
        # Determine if multiple choice
        is_multiple_choice = len(correct_answer) > 1 or "Choose two" in question_text or "Choose three" in question_text
        
        # Extract community voting data
        voting_data = extract_voting_data(content, question_id)
        
        question_obj = {
            "id": f"Question #{question_num}",
            "text": question_text,
            "options": options,
            "correctAnswer": correct_answer,
            "multipleChoice": is_multiple_choice,
            "communityVoting": voting_data,
            "examTopicsId": question_id
        }
        
        questions.append(question_obj)
        print(f"Extracted Question #{question_num} (ID: {question_id})")
    
    return questions

def main():
    """Main extraction process"""
    print("🔄 Starting VCF Architect Exam Question Extraction...")
    
    # Extract from both HTML files
    page1_questions = extract_questions_from_html("2V0-13.24 Exam - Free Actual Q&As, Page 1 _ ExamTopics.html")
    page2_questions = extract_questions_from_html("2V0-13.24 Exam - Free Actual Q&As, Page 2 _ ExamTopics.html")
    
    # Combine all questions
    all_questions = page1_questions + page2_questions
    
    # Sort by question number
    all_questions.sort(key=lambda x: int(x["id"].split("#")[1]))
    
    print(f"\n✅ Extracted {len(all_questions)} questions total")
    print(f"   📄 Page 1: {len(page1_questions)} questions")
    print(f"   📄 Page 2: {len(page2_questions)} questions")
    
    # Generate JavaScript file content
    js_content = """// VCF Architect Exam Questions Data - All 60 Questions Extracted from ExamTopics
// Community voting data prioritized for correct answers
// Source: VMware 2V0-13.24 (VMware Cloud Foundation 5.2 Architect) ExamTopics
// Accurately extracted from source HTML with exact formatting
const examQuestions = [
"""
    
    for i, question in enumerate(all_questions):
        js_content += "    {\n"
        js_content += f'        id: "{question["id"]}",\n'
        js_content += f'        text: "{question["text"]}",\n'
        js_content += "        options: [\n"
        for option in question["options"]:
            js_content += f'            "{option}",\n'
        js_content += "        ],\n"
        js_content += f'        correctAnswer: "{question["correctAnswer"]}",\n'
        js_content += f'        multipleChoice: {str(question["multipleChoice"]).lower()},\n'
        js_content += f'        communityVoting: {json.dumps(question["communityVoting"])},\n'
        js_content += f'        examTopicsId: "{question["examTopicsId"]}"\n'
        js_content += "    }"
        if i < len(all_questions) - 1:
            js_content += ","
        js_content += "\n"
    
    js_content += "];\n"
    
    # Write to file
    with open("architect exam/exam-questions-new.js", "w", encoding="utf-8") as f:
        f.write(js_content)
    
    print(f"\n🎉 Successfully generated exam-questions-new.js with {len(all_questions)} questions!")
    print("📝 Review the file and replace the original when ready.")

if __name__ == "__main__":
    main()
